<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - KYX 签到系统</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        /* 管理后台专属样式 */
        .admin-container {
            min-height: 100vh;
            background: var(--color-bg-primary);
            display: flex;
        }

        /* 侧边栏 */
        .sidebar {
            width: 260px;
            background: var(--color-bg-elevated);
            border-right: 1px solid var(--color-border-primary);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: var(--z-sticky);
        }

        .sidebar-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--color-border-primary);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--text-xl);
            font-weight: var(--font-bold);
            color: var(--color-text-primary);
        }

        .sidebar-nav {
            flex: 1;
            padding: var(--spacing-md);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: var(--spacing-xl);
        }

        .nav-section-title {
            font-size: var(--text-xs);
            font-weight: var(--font-semibold);
            color: var(--color-text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--spacing-sm);
            padding: 0 var(--spacing-sm);
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-lg);
            color: var(--color-text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
            margin-bottom: var(--spacing-xs);
        }

        .nav-item:hover {
            background: var(--color-bg-secondary);
            color: var(--color-text-primary);
        }

        .nav-item.active {
            background: var(--color-accent);
            color: var(--color-text-inverse);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 主内容区 */
        .main-area {
            flex: 1;
            margin-left: 260px;
            display: flex;
            flex-direction: column;
        }

        /* 顶部栏 */
        .topbar {
            background: var(--color-bg-elevated);
            border-bottom: 1px solid var(--color-border-primary);
            padding: var(--spacing-md) var(--spacing-xl);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: var(--z-sticky);
        }

        .topbar-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-semibold);
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        /* 内容区 */
        .content {
            flex: 1;
            padding: var(--spacing-xl);
            overflow-y: auto;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }

        .stat-card {
            background: var(--color-bg-elevated);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-accent), var(--color-accent-hover));
        }

        .stat-label {
            font-size: var(--text-sm);
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .stat-value {
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            color: var(--color-text-primary);
        }

        .stat-change {
            font-size: var(--text-sm);
            color: var(--color-success);
            margin-top: var(--spacing-xs);
        }

        /* 数据表格 */
        .data-table {
            background: var(--color-bg-elevated);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--radius-xl);
            overflow: hidden;
        }

        .table-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--color-border-primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .table-title {
            font-size: var(--text-lg);
            font-weight: var(--font-semibold);
        }

        .table-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .table-content {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            background: var(--color-bg-secondary);
            border-bottom: 1px solid var(--color-border-primary);
        }

        th {
            padding: var(--spacing-md);
            text-align: left;
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            color: var(--color-text-secondary);
        }

        td {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--color-border-secondary);
            font-size: var(--text-sm);
        }

        tbody tr:hover {
            background: var(--color-bg-secondary);
        }

        tbody tr:last-child td {
            border-bottom: none;
        }

        /* 上传区域 */
        .upload-zone {
            background: var(--color-bg-elevated);
            border: 2px dashed var(--color-border-primary);
            border-radius: var(--radius-xl);
            padding: var(--spacing-3xl);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .upload-zone:hover,
        .upload-zone.dragover {
            border-color: var(--color-accent);
            background: var(--color-bg-secondary);
        }

        .upload-icon {
            font-size: var(--text-5xl);
            margin-bottom: var(--spacing-md);
            opacity: 0.5;
        }

        .upload-text {
            color: var(--color-text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .upload-hint {
            font-size: var(--text-sm);
            color: var(--color-text-tertiary);
        }

        /* 库存面板 */
        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .inventory-item {
            background: var(--color-bg-elevated);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            text-align: center;
            transition: all var(--transition-fast);
        }

        .inventory-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .inventory-amount {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--color-accent);
            margin-bottom: var(--spacing-xs);
        }

        .inventory-count {
            font-size: var(--text-sm);
            color: var(--color-text-secondary);
        }

        .inventory-bar {
            height: 4px;
            background: var(--color-bg-tertiary);
            border-radius: var(--radius-full);
            margin-top: var(--spacing-sm);
            overflow: hidden;
        }

        .inventory-bar-fill {
            height: 100%;
            background: var(--color-success);
            transition: width var(--transition-base);
        }

        /* 待分配用户 */
        .pending-list {
            display: grid;
            gap: var(--spacing-md);
        }

        .pending-item {
            background: var(--color-bg-elevated);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .pending-user {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .pending-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: var(--color-bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .pending-info {
            display: flex;
            flex-direction: column;
        }

        .pending-name {
            font-weight: var(--font-medium);
            color: var(--color-text-primary);
        }

        .pending-date {
            font-size: var(--text-sm);
            color: var(--color-text-tertiary);
        }

        /* 标签页 */
        .tabs {
            display: flex;
            gap: var(--spacing-xs);
            border-bottom: 1px solid var(--color-border-primary);
            margin-bottom: var(--spacing-xl);
        }

        .tab {
            padding: var(--spacing-sm) var(--spacing-lg);
            color: var(--color-text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-fast);
        }

        .tab:hover {
            color: var(--color-text-primary);
        }

        .tab.active {
            color: var(--color-accent);
            border-bottom-color: var(--color-accent);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 响应式 */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform var(--transition-base);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-area {
                margin-left: 0;
            }

            .mobile-menu-btn {
                display: flex;
            }
        }

        @media (min-width: 1025px) {
            .mobile-menu-btn {
                display: none;
            }
        }

        /* 加载状态 */
        .loading-skeleton {
            background: linear-gradient(
                90deg,
                var(--color-bg-secondary) 25%,
                var(--color-bg-tertiary) 50%,
                var(--color-bg-secondary) 75%
            );
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: var(--radius-md);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <span>⚙️</span>
                    <span>管理后台</span>
                </div>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">概览</div>
                    <div class="nav-item active" data-page="dashboard">
                        <span class="nav-icon">📊</span>
                        <span>仪表盘</span>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">兑换码管理</div>
                    <div class="nav-item" data-page="upload">
                        <span class="nav-icon">📤</span>
                        <span>上传兑换码</span>
                    </div>
                    <div class="nav-item" data-page="inventory">
                        <span class="nav-icon">📦</span>
                        <span>库存管理</span>
                    </div>
                    <div class="nav-item" data-page="codes">
                        <span class="nav-icon">🎫</span>
                        <span>兑换码列表</span>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">分发管理</div>
                    <div class="nav-item" data-page="pending">
                        <span class="nav-icon">⏳</span>
                        <span>待分配用户</span>
                    </div>
                    <div class="nav-item" data-page="gift">
                        <span class="nav-icon">🎁</span>
                        <span>系统赠送</span>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">数据统计</div>
                    <div class="nav-item" data-page="redemptions">
                        <span class="nav-icon">📈</span>
                        <span>签到记录</span>
                    </div>
                    <div class="nav-item" data-page="users">
                        <span class="nav-icon">👥</span>
                        <span>用户管理</span>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-area">
            <!-- 顶部栏 -->
            <header class="topbar">
                <button class="btn btn-icon mobile-menu-btn" id="mobileMenuBtn">
                    <span>☰</span>
                </button>
                <h1 class="topbar-title" id="pageTitle">仪表盘</h1>
                <div class="topbar-actions">
                    <button class="btn btn-icon" id="themeToggle">
                        <span id="themeIcon">🌙</span>
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="window.location.href='index.html'">
                        返回前台
                    </button>
                    <button class="btn btn-sm btn-ghost" id="logoutBtn">
                        退出登录
                    </button>
                </div>
            </header>

            <!-- 内容区 -->
            <div class="content" id="content">
                <!-- 仪表盘页面 -->
                <div class="page-content active" id="page-dashboard">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">总兑换码数</div>
                            <div class="stat-value" id="statTotal">-</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">未使用</div>
                            <div class="stat-value" id="statUnused">-</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">已使用</div>
                            <div class="stat-value" id="statUsed">-</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">使用率</div>
                            <div class="stat-value" id="statRate">-</div>
                        </div>
                    </div>

                    <div class="data-table">
                        <div class="table-header">
                            <h2 class="table-title">最近上传记录</h2>
                        </div>
                        <div class="table-content">
                            <table>
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>金额</th>
                                        <th>总数</th>
                                        <th>新增</th>
                                        <th>重复</th>
                                        <th>上传时间</th>
                                    </tr>
                                </thead>
                                <tbody id="recentUploads">
                                    <tr>
                                        <td colspan="6" style="text-align: center;">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 上传兑换码页面 -->
                <div class="page-content" id="page-upload">
                    <div class="card">
                        <div class="card-body">
                            <h2 class="mb-4">上传兑换码</h2>
                            
                            <div class="mb-4">
                                <label class="label">设置金额</label>
                                <input type="number" class="input" id="uploadAmount" placeholder="请输入兑换码金额" min="0.01" step="0.01">
                            </div>

                            <div class="upload-zone" id="uploadZone">
                                <div class="upload-icon">📁</div>
                                <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                                <div class="upload-hint">支持 .txt 文件，每行一个兑换码</div>
                                <input type="file" id="fileInput" accept=".txt" style="display: none;">
                            </div>

                            <div id="uploadResult" style="margin-top: 20px;"></div>
                        </div>
                    </div>
                </div>

                <!-- 库存管理页面 -->
                <div class="page-content" id="page-inventory">
                    <h2 class="mb-4">库存统计</h2>
                    <div class="inventory-grid" id="inventoryGrid">
                        <div class="skeleton" style="height: 100px;"></div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <h3 class="mb-3">批量操作</h3>
                            <button class="btn btn-primary" onclick="refreshInventory()">刷新库存</button>
                            <button class="btn btn-secondary" onclick="exportInventory()">导出报表</button>
                            <button class="btn btn-warning" onclick="setDefaultCheckinAmount()">设置签到金额</button>
                        </div>
                    </div>
                </div>

                <!-- 兑换码列表页面 -->
                <div class="page-content" id="page-codes">
                    <div class="data-table">
                        <div class="table-header">
                            <h2 class="table-title">兑换码列表</h2>
                            <div class="table-actions">
                                <select class="input" id="codeStatusFilter" style="width: 120px;">
                                    <option value="">全部状态</option>
                                    <option value="unused">未使用</option>
                                    <option value="used">已使用</option>
                                </select>
                                <select class="input" id="codeAmountFilter" style="width: 120px;">
                                    <option value="">全部金额</option>
                                </select>
                                <button class="btn btn-sm btn-primary" onclick="loadCodes()">刷新</button>
                                <button class="btn btn-sm btn-danger" onclick="clearAllUnusedCodes()">一键清空</button>
                            </div>
                        </div>
                        <div class="table-content">
                            <table>
                                <thead>
                                    <tr>
                                        <th>兑换码</th>
                                        <th>金额</th>
                                        <th>发放用户</th>
                                        <th>发放时间</th>
                                        <th>发放状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="codesList">
                                    <tr>
                                        <td colspan="6" style="text-align: center;">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 待分配用户页面 -->
                <div class="page-content" id="page-pending">
                    <div class="flex justify-between items-center mb-4">
                        <h2>待分配用户</h2>
                        <button class="btn btn-primary" onclick="resolvePending()">
                            一键补发
                        </button>
                    </div>
                    <div class="pending-list" id="pendingList">
                        <div class="skeleton" style="height: 80px;"></div>
                    </div>
                </div>

                <!-- 系统赠送页面 -->
                <div class="page-content" id="page-gift">
                    <div class="card">
                        <div class="card-body">
                            <h2 class="mb-4">系统赠送</h2>
                            
                            <div class="mb-3">
                                <label class="label">选择用户</label>
                                <select class="input" id="giftUsers" multiple style="height: 200px;">
                                    <option>加载中...</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="label">选择金额</label>
                                <select class="input" id="giftAmount">
                                    <option value="">请选择金额</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="label">赠送消息（可选）</label>
                                <textarea class="input" id="giftMessage" rows="3" placeholder="恭喜您获得系统赠送的兑换码！"></textarea>
                            </div>

                            <button class="btn btn-primary btn-block" onclick="sendGift()">
                                确认赠送
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 签到记录页面 -->
                <div class="page-content" id="page-redemptions">
                    <div class="data-table">
                        <div class="table-header">
                            <h2 class="table-title">签到记录</h2>
                            <div class="table-actions">
                                <input type="text" class="input" id="searchInput" placeholder="搜索用户名或兑换码" style="width: 200px;">
                                <button class="btn btn-sm btn-primary" onclick="searchRedemptions()">搜索</button>
                                <button class="btn btn-sm btn-secondary" onclick="toggleRedemptionsSort()">
                                    切换排序
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="setDefaultCheckinAmount()">
                                    设置签到金额
                                </button>
                                <span style="margin-left: 10px; color: var(--color-text-secondary);">
                                    当前: <span id="currentCheckinAmount">未设置</span>
                                </span>
                            </div>
                        </div>
                        <div class="table-content">
                            <table>
                                <thead>
                                    <tr>
                                        <th style="cursor: pointer;" onclick="toggleRedemptionsSort()">
                                            签到时间 ↕
                                        </th>
                                        <th>用户名</th>
                                        <th>Linux Do ID</th>
                                        <th>发放的兑换码</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="redemptionsList">
                                    <tr>
                                        <td colspan="7" style="text-align: center;">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 用户管理页面 -->
                <div class="page-content" id="page-users">
                    <div class="data-table">
                        <div class="table-header">
                            <h2 class="table-title">用户列表</h2>
                            <div class="table-actions">
                                <span id="selectedCount" style="margin-right: 10px;">已选择 0 个用户</span>
                                <button class="btn btn-sm btn-primary batch-action-btn" onclick="batchDistributeToSelected()" disabled>
                                    批量发放
                                </button>
                            </div>
                        </div>
                        <div class="table-content">
                            <table>
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAllUsers" onchange="toggleSelectAll()">
                                        </th>
                                        <th>用户名</th>
                                        <th>Linux Do ID</th>
                                        <th>签到次数</th>
                                        <th>获得兑换码</th>
                                        <th>待发放</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="usersList">
                                    <tr>
                                        <td colspan="7" style="text-align: center;">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Toast容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>