/* 全局变量 */
:root {
    --primary-color: #4F46E5;
    --primary-hover: #4338CA;
    --success-color: #10B981;
    --error-color: #EF4444;
    --warning-color: #F59E0B;
    --bg-color: #F9FAFB;
    --card-bg: #FFFFFF;
    --text-primary: #111827;
    --text-secondary: #6B7280;
    --text-muted: #9CA3AF;
    --border-color: #E5E7EB;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部 */
.header {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-primary);
}

.logo a {
    color: inherit;
    text-decoration: none;
}

/* 用户信息 */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 500;
}

.logout-btn {
    padding: 0.5rem 1rem;
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s;
}

.logout-btn:hover {
    background-color: var(--bg-color);
}

/* 主内容区 */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

/* 登录按钮 */
.login-btn, .login-btn-large {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.login-btn:hover, .login-btn-large:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.login-btn-large {
    font-size: 1.125rem;
    padding: 1rem 2rem;
}

/* 签到区域 */
.checkin-section {
    margin-bottom: 2rem;
}

.checkin-card {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    text-align: center;
}

.checkin-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 3rem;
    font-size: 1.25rem;
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), #6366F1);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.checkin-btn:hover:not(.disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

.checkin-btn:active:not(.disabled) {
    transform: translateY(0);
}

.checkin-btn.disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    box-shadow: none;
}

.checkin-btn .icon {
    font-size: 1.5rem;
}

.checkin-status {
    margin-top: 1rem;
    font-size: 1.125rem;
    color: var(--text-secondary);
}

/* 统计信息 */
.stats-container {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* 快捷导航 */
.quick-nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.nav-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--bg-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all 0.2s;
}

.nav-link:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    text-decoration: none;
}

/* 日历 */
.calendar-section {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.calendar-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.calendar-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 1.25rem;
}

.calendar-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.calendar-month {
    font-size: 1.25rem;
    font-weight: 600;
    min-width: 150px;
    text-align: center;
}

.calendar {
    max-width: 500px;
    margin: 0 auto;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.calendar-day-name {
    text-align: center;
    font-weight: 600;
    color: var(--text-secondary);
    padding: 0.5rem;
}

.calendar-body {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.calendar-day:hover {
    background-color: var(--bg-color);
}

.calendar-day.checked {
    background-color: var(--success-color);
    color: white;
}

.calendar-day.today {
    border: 2px solid var(--primary-color);
}

.calendar-day.other-month {
    color: var(--text-muted);
}

/* 登录提示 */
.login-prompt {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.prompt-card {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    max-width: 500px;
}

.prompt-card h2 {
    margin-bottom: 1rem;
}

.prompt-card p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* 弹窗 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.2s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--radius-lg);
    max-width: 400px;
    width: 90%;
    text-align: center;
    animation: slideIn 0.3s ease;
}

.modal-content h3 {
    margin-bottom: 1rem;
}

.code-display {
    display: flex;
    gap: 0.5rem;
    margin: 1.5rem 0;
}

.code-display input {
    flex: 1;
    padding: 0.75rem;
    font-size: 1.125rem;
    font-family: monospace;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    text-align: center;
    background-color: var(--bg-color);
}

.copy-btn {
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.copy-btn:hover {
    background-color: var(--primary-hover);
}

.close-btn {
    padding: 0.75rem 2rem;
    background-color: var(--bg-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.close-btn:hover {
    background-color: var(--border-color);
}

/* 兑换码列表 */
.codes-section {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.search-box {
    display: flex;
    gap: 0.5rem;
}

.search-box input {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    width: 250px;
}

.search-btn {
    padding: 0.5rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.search-btn:hover {
    background-color: var(--primary-hover);
}

.codes-list {
    display: grid;
    gap: 1rem;
}

.code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all 0.2s;
}

.code-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.code-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.code-text {
    font-family: monospace;
    font-size: 1.125rem;
    font-weight: 600;
}

.code-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.code-actions {
    display: flex;
    gap: 0.5rem;
}

.code-actions button {
    padding: 0.5rem 1rem;
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.code-actions button:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination button {
    padding: 0.5rem 1rem;
    background-color: var(--bg-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s;
}

.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination .page-info {
    padding: 0 1rem;
    color: var(--text-secondary);
}

/* 登录页面 */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
}

.login-card {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    max-width: 450px;
    width: 100%;
}

.login-title {
    text-align: center;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.login-subtitle {
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.login-features {
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    margin-bottom: 0.75rem;
}

.feature-icon {
    font-size: 1.5rem;
}

.feature-text {
    color: var(--text-primary);
}

.login-btn-primary {
    width: 100%;
    padding: 1rem;
    font-size: 1.125rem;
    justify-content: center;
}

.login-icon {
    width: 24px;
    height: 24px;
}

.login-note {
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 1.5rem;
}

/* 加载动画 */
.loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
}

.loading.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Toast 提示 */
.toast {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    padding: 1rem 2rem;
    background-color: var(--text-primary);
    color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1001;
}

.toast.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--error-color);
}

.toast.warning {
    background-color: var(--warning-color);
}

/* 动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stats-container {
        gap: 2rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .search-box {
        width: 100%;
    }
    
    .search-box input {
        flex: 1;
    }
    
    .code-item {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .code-actions {
        justify-content: flex-end;
    }
    
    .calendar-controls {
        gap: 0.5rem;
    }
    
    .calendar-month {
        font-size: 1rem;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .checkin-btn {
        padding: 1rem 2rem;
        font-size: 1.125rem;
    }
    
    .login-card {
        padding: 2rem;
    }
    
    .modal-content {
        padding: 1.5rem;
    }
}