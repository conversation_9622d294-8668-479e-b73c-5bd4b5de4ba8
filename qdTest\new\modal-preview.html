<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗UI预览 - KYX 签到系统</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 50%, #2d2d2d 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .preview-container {
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .preview-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #feca57, #ff9ff3, #48dbfb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .preview-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.2rem;
            margin-bottom: 3rem;
            line-height: 1.6;
        }

        .preview-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .preview-btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            border: none;
            border-radius: 2rem;
            color: #000;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(254, 202, 87, 0.3);
        }

        .preview-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(254, 202, 87, 0.5);
        }

        .preview-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .preview-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(254, 202, 87, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #fff;
        }

        .feature-desc {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 优化后的赠送弹窗样式 */
        .gift-modal {
            max-width: 580px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }

        .gift-modal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 300% 100%;
            animation: rainbow 3s ease infinite;
        }

        @keyframes rainbow {

            0%,
            100% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }
        }

        .gift-modal-header {
            text-align: center;
            padding: 2rem 2rem 1rem;
            position: relative;
        }

        .gift-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-10px);
            }

            60% {
                transform: translateY(-5px);
            }
        }

        .gift-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gift-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            margin: 0;
        }

        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            transform: scale(1.1);
        }

        .gift-modal-body {
            padding: 1rem 2rem 2rem;
        }

        .gift-amount {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(254, 202, 87, 0.1), rgba(255, 159, 243, 0.1));
            border-radius: 1rem;
            border: 1px solid rgba(254, 202, 87, 0.2);
        }

        .currency {
            font-size: 1.5rem;
            color: #feca57;
            font-weight: 600;
        }

        .amount {
            font-size: 3rem;
            font-weight: 800;
            color: #fff;
            margin-left: 0.25rem;
            text-shadow: 0 0 20px rgba(254, 202, 87, 0.5);
        }

        .gift-code-container {
            margin-top: 1.5rem;
        }

        .code-label {
            display: block;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .code-input-group {
            display: flex;
            gap: 0.75rem;
            align-items: stretch;
        }

        .gift-code-input {
            flex: 1;
            padding: 0.75rem 1rem;
            background: rgba(139, 184, 232, 0.1);
            border: 1px solid rgba(139, 184, 232, 0.2);
            border-radius: 0.375rem;
            color: oklch(0.707 0.022 261.325);
            font-family: "Inter", "Inter Fallback", ui-monospace, "SF Mono", "Cascadia Code", "JetBrains Mono", "Fira Code", "Roboto Mono", "Consolas", monospace;
            font-size: 1.125rem;
            font-weight: 400;
            text-align: center;
            letter-spacing: 0.05em;
            font-feature-settings: "liga" 0, "calt" 0;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .gift-code-input:focus {
            outline: none;
            border-color: #feca57;
            box-shadow: 0 0 0 3px rgba(254, 202, 87, 0.2);
        }

        .copy-code-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.25rem;
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            border: none;
            border-radius: 0.75rem;
            color: #000;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .copy-code-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(254, 202, 87, 0.4);
        }

        .copy-code-btn:active {
            transform: translateY(0);
        }

        .gift-modal-footer {
            padding: 1rem 2rem 2rem;
            text-align: center;
        }

        .btn-close-gift {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 2rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 2rem;
            color: #fff;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-close-gift:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .preview-title {
                font-size: 2rem;
            }

            .preview-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .gift-modal {
                max-width: 90vw;
                margin: 1rem;
            }

            .gift-modal-header {
                padding: 1.5rem 1.5rem 1rem;
            }

            .gift-icon {
                font-size: 3rem;
            }

            .gift-title {
                font-size: 1.5rem;
            }

            .gift-modal-body {
                padding: 1rem 1.5rem 1.5rem;
            }

            .gift-amount {
                padding: 1rem;
            }

            .amount {
                font-size: 2.5rem;
            }

            .code-input-group {
                flex-direction: column;
                gap: 0.5rem;
            }

            .copy-code-btn {
                justify-content: center;
            }

            .gift-modal-footer {
                padding: 1rem 1.5rem 1.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="preview-container">
        <h1 class="preview-title">🎁 弹窗UI优化预览</h1>
        <p class="preview-subtitle">
            全新设计的系统赠送弹窗<br>
            现代化 · 美观 · 用户友好
        </p>

        <div class="preview-buttons">
            <button class="preview-btn" onclick="showGiftModal()">
                🎁 预览赠送弹窗
            </button>
            <button class="preview-btn secondary" onclick="window.location.href='test-optimization.html'">
                🧪 完整测试页面
            </button>
            <button class="preview-btn secondary" onclick="window.location.href='index.html'">
                🏠 返回主页
            </button>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🌈</div>
                <div class="feature-title">彩虹装饰</div>
                <div class="feature-desc">顶部流动的彩虹装饰条，增加视觉活力和现代感</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🎭</div>
                <div class="feature-title">动画效果</div>
                <div class="feature-desc">礼物图标弹跳动画，按钮悬停效果，提升交互体验</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <div class="feature-title">金额展示</div>
                <div class="feature-desc">突出的金额显示区域，发光效果增强价值感知</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">响应式设计</div>
                <div class="feature-desc">完美适配桌面和移动设备，优化触摸操作体验</div>
            </div>
        </div>
    </div>

    <!-- Toast容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- 赠送弹窗 -->
    <div class="modal" id="previewGiftModal">
        <div class="modal-backdrop" onclick="closeGiftModal()"></div>
        <div class="modal-content gift-modal">
            <div class="gift-modal-header">
                <div class="gift-icon">🎁</div>
                <h3 class="gift-title">系统赠送</h3>
                <p class="gift-subtitle">恭喜您获得兑换码奖励！</p>
                <button class="modal-close" onclick="closeGiftModal()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                    </svg>
                </button>
            </div>
            <div class="gift-modal-body">
                <div class="gift-amount">
                    <span class="currency">$</span>
                    <span class="amount">20</span>
                </div>
                <div class="gift-code-container">
                    <label class="code-label">您的兑换码</label>
                    <div class="code-input-group">
                        <input type="text" id="previewGiftCodeInput" readonly value="PREVIEW123456789"
                            class="gift-code-input">
                        <button class="copy-code-btn" onclick="copyGiftCode()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor"
                                    stroke-width="2" />
                                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke="currentColor"
                                    stroke-width="2" />
                            </svg>
                            <span>复制</span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="gift-modal-footer">
                <button class="btn-close-gift" onclick="closeGiftModal()">
                    <span>我知道了</span>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M5 12h14m-7-7l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script>
        function showGiftModal() {
            document.getElementById('previewGiftModal').classList.add('active');
        }

        function closeGiftModal() {
            document.getElementById('previewGiftModal').classList.remove('active');
        }

        function copyGiftCode() {
            const code = document.getElementById('previewGiftCodeInput').value;
            navigator.clipboard.writeText(code).then(() => {
                utils.showToast('预览兑换码已复制！', 'success');
            }).catch(() => {
                utils.showToast('复制失败，请手动复制', 'error');
            });
        }
    </script>
</body>

</html>