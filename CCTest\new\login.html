<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用 KYX 签到系统</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        /* 登录页面专属样式 */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #0f0f0f;
            position: relative;
            overflow: hidden;
        }

        /* 背景装饰 */
        .bg-decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .bg-decoration::before {
            content: '';
            position: absolute;
            width: 200%;
            height: 200%;
            top: -50%;
            left: -50%;
            background: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translate(0, 0) rotate(0deg);
            }

            33% {
                transform: translate(-20px, -20px) rotate(1deg);
            }

            66% {
                transform: translate(20px, -10px) rotate(-1deg);
            }
        }

        /* 网格背景 */
        .grid-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            opacity: 0.3;
        }

        /* 登录卡片 */
        .login-card {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 420px;
            padding: var(--spacing-2xl);
            text-align: center;
        }

        /* Logo区域 */
        .logo-container {
            margin-bottom: var(--spacing-2xl);
            animation: fadeInDown 0.8s ease-out;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--spacing-lg);
            background: linear-gradient(135deg, #fff 0%, #e0e0e0 100%);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-3xl);
            box-shadow: 0 10px 40px rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: translateX(-100%);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        .logo-text {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: #fff;
            margin-bottom: var(--spacing-sm);
            letter-spacing: 2px;
        }

        .logo-subtitle {
            font-size: var(--text-sm);
            color: var(--color-text-tertiary);
            letter-spacing: 1px;
        }

        /* 登录按钮 */
        .login-btn {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-xl);
            background: #fff;
            color: #0f0f0f;
            border: none;
            border-radius: var(--radius-lg);
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            cursor: pointer;
            transition: all var(--transition-base);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-btn-text {
            flex: 1;
        }

        /* 管理员登录按钮 */
        .admin-login-trigger {
            position: fixed; /* 改为 fixed 确保不被其他元素遮挡 */
            top: 24px; /* var(--spacing-lg) 后备值 */
            right: 24px; /* var(--spacing-lg) 后备值 */
            padding: 8px 16px; /* var(--spacing-sm) var(--spacing-md) 后备值 */
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px; /* var(--radius-md) 后备值 */
            color: #adb5bd; /* var(--color-text-secondary) 后备值 */
            font-size: 14px; /* var(--text-sm) 后备值 */
            cursor: pointer;
            transition: all 0.25s; /* var(--transition-base) 后备值 */
            backdrop-filter: blur(10px);
            z-index: 1000; /* 提高 z-index */
            pointer-events: auto; /* 确保可以接收点击事件 */
        }

        .admin-login-trigger:hover {
            background: rgba(255, 255, 255, 0.15);
            color: #fff;
            border-color: rgba(255, 255, 255, 0.3);
        }

        /* 管理员登录模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }

        .modal.active {
            display: flex !important;
        }

        .modal-content {
            background: #1a1a1a; /* var(--color-bg-elevated) 后备值 */
            border: 1px solid #2c2c2c; /* var(--color-border-primary) 后备值 */
            border-radius: 16px; /* var(--radius-xl) 后备值 */
            padding: 32px; /* var(--spacing-2xl) 后备值 */
            width: 90%;
            max-width: 400px;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            text-align: center;
            margin-bottom: 32px; /* var(--spacing-xl) 后备值 */
        }

        .modal-title {
            font-size: 20px; /* var(--text-xl) 后备值 */
            font-weight: 600; /* var(--font-semibold) 后备值 */
            color: #f8f9fa; /* var(--color-text-primary) 后备值 */
            margin: 0;
        }

        .form-group {
            margin-bottom: 24px; /* var(--spacing-lg) 后备值 */
        }

        .form-label {
            display: block;
            margin-bottom: 8px; /* var(--spacing-sm) 后备值 */
            font-size: 14px; /* var(--text-sm) 后备值 */
            color: #adb5bd; /* var(--color-text-secondary) 后备值 */
        }

        .form-input {
            width: 100%;
            padding: 8px 16px; /* var(--spacing-sm) var(--spacing-md) 后备值 */
            background: #262626; /* var(--color-bg-secondary) 后备值 */
            border: 1px solid #2c2c2c; /* var(--color-border-secondary) 后备值 */
            border-radius: 8px; /* var(--radius-md) 后备值 */
            color: #f8f9fa; /* var(--color-text-primary) 后备值 */
            font-size: 16px; /* var(--text-base) 后备值 */
            transition: all 0.15s; /* var(--transition-fast) 后备值 */
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #f8f9fa; /* var(--color-accent) 后备值 */
            background: #0f0f0f; /* var(--color-bg-primary) 后备值 */
        }

        .form-actions {
            display: flex;
            gap: 16px; /* var(--spacing-md) 后备值 */
            margin-top: 32px; /* var(--spacing-xl) 后备值 */
        }

        .btn {
            flex: 1;
            padding: 8px 24px; /* var(--spacing-sm) var(--spacing-lg) 后备值 */
            border: none;
            border-radius: 8px; /* var(--radius-md) 后备值 */
            font-size: 16px; /* var(--text-base) 后备值 */
            font-weight: 500; /* var(--font-medium) 后备值 */
            cursor: pointer;
            transition: all 0.25s; /* var(--transition-base) 后备值 */
        }

        .btn-primary {
            background: #f8f9fa; /* var(--color-accent) 后备值 */
            color: #0f0f0f; /* var(--color-text-inverse) 后备值 */
        }

        .btn-primary:hover {
            background: #e9ecef; /* var(--color-accent-hover) 后备值 */
        }

        .btn-secondary {
            background: #262626; /* var(--color-bg-secondary) 后备值 */
            color: #adb5bd; /* var(--color-text-secondary) 后备值 */
            border: 1px solid #2c2c2c; /* var(--color-border-secondary) 后备值 */
        }

        .btn-secondary:hover {
            background: #2c2c2c; /* var(--color-bg-tertiary) 后备值 */
            color: #f8f9fa; /* var(--color-text-primary) 后备值 */
        }

        /* 服务条款 */
        .terms {
            margin-top: var(--spacing-xl);
            font-size: var(--text-xs);
            color: var(--color-text-tertiary);
            line-height: var(--leading-relaxed);
            animation: fadeIn 0.8s ease-out 0.4s both;
        }

        .terms a {
            color: var(--color-text-secondary);
            text-decoration: underline;
            transition: color var(--transition-fast);
        }

        .terms a:hover {
            color: #fff;
        }

        /* 动画 */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        /* 加载状态 */
        .loading-state {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 15, 15, 0.9);
            backdrop-filter: blur(4px);
            border-radius: var(--radius-lg);
            align-items: center;
            justify-content: center;
            z-index: 20;
        }

        .loading-state.active {
            display: flex;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top-color: #fff;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }

        /* 错误提示 */
        .error-message {
            display: none;
            margin-top: var(--spacing-md);
            padding: var(--spacing-sm) var(--spacing-md);
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: var(--radius-md);
            color: #ef4444;
            font-size: var(--text-sm);
            animation: shake 0.5s ease-in-out;
        }

        .error-message.active {
            display: block;
        }

        @keyframes shake {

            0%,
            100% {
                transform: translateX(0);
            }

            10%,
            30%,
            50%,
            70%,
            90% {
                transform: translateX(-5px);
            }

            20%,
            40%,
            60%,
            80% {
                transform: translateX(5px);
            }
        }

        /* 响应式 */
        @media (max-width: 480px) {
            .login-card {
                padding: var(--spacing-xl);
            }

            .logo-icon {
                width: 60px;
                height: 60px;
                font-size: var(--text-2xl);
            }

            .logo-text {
                font-size: var(--text-xl);
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="bg-decoration"></div>
        <div class="grid-bg"></div>

        <!-- 管理员登录入口 -->
        <button class="admin-login-trigger" id="adminLoginTrigger">
            管理员登录
        </button>

        <!-- 登录卡片 -->
        <div class="login-card">
            <div class="logo-container">
                <div class="logo-icon">
                    <span style="color: #0f0f0f;">📅</span>
                </div>
                <h1 class="logo-text">KYX 签到系统</h1>
                <p class="logo-subtitle">每日签到 · 领取兑换码</p>
            </div>

            <button class="login-btn" id="loginBtn">
                <span class="login-btn-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </span>
                <span class="login-btn-text">使用 Linux Do 账户登录</span>
            </button>

            <div class="error-message" id="errorMessage"></div>

            <div class="loading-state" id="loadingState">
                <div class="loading-spinner"></div>
            </div>

            <p class="terms">
                登录即表示您同意我们的
                <a href="#" onclick="event.preventDefault()">服务条款</a>
                和
                <a href="#" onclick="event.preventDefault()">隐私政策</a>
            </p>
        </div>
    </div>

    <!-- 管理员登录模态框 -->
    <div class="modal" id="adminLoginModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">管理员登录</h2>
            </div>
            <form id="adminLoginForm">
                <div class="form-group">
                    <label class="form-label" for="adminUsername">用户名</label>
                    <input type="text" id="adminUsername" class="form-input" required placeholder="请输入管理员用户名">
                </div>
                <div class="form-group">
                    <label class="form-label" for="adminPassword">密码</label>
                    <input type="password" id="adminPassword" class="form-input" required placeholder="请输入管理员密码">
                </div>
                <div class="error-message" id="adminErrorMessage"></div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelAdminLogin">取消</button>
                    <button type="submit" class="btn btn-primary">登录</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        // 使用配置系统中的 API 地址，如果配置未加载则使用默认值
        const API_URL = window.CONFIG?.API_BASE_URL || (window.location.hostname === 'localhost'
            ? 'http://localhost:8787'
            : 'https://apisign.kkyyxx.xyz');

        // 获取元素
        const loginBtn = document.getElementById('loginBtn');
        const loadingState = document.getElementById('loadingState');
        const errorMessage = document.getElementById('errorMessage');
        const adminLoginTrigger = document.getElementById('adminLoginTrigger');
        const adminLoginModal = document.getElementById('adminLoginModal');
        const adminLoginForm = document.getElementById('adminLoginForm');
        const adminErrorMessage = document.getElementById('adminErrorMessage');
        const cancelAdminLogin = document.getElementById('cancelAdminLogin');

        // 调试信息
        console.log('页面元素检查:', {
            loginBtn: !!loginBtn,
            adminLoginTrigger: !!adminLoginTrigger,
            adminLoginModal: !!adminLoginModal,
            adminLoginForm: !!adminLoginForm
        });

        // 显示错误信息
        function showError(message, isAdmin = false) {
            const errorEl = isAdmin ? adminErrorMessage : errorMessage;
            errorEl.textContent = message;
            errorEl.classList.add('active');
            setTimeout(() => {
                errorEl.classList.remove('active');
            }, 5000);
        }

        // 显示加载状态
        function showLoading(show = true) {
            if (show) {
                loadingState.classList.add('active');
                loginBtn.disabled = true;
            } else {
                loadingState.classList.remove('active');
                loginBtn.disabled = false;
            }
        }

        // 处理OAuth登录
        async function handleLogin() {
            showLoading(true);
            errorMessage.classList.remove('active');

            try {
                const response = await fetch(`${API_URL}/api/auth/login`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('获取登录链接失败');
                }

                const data = await response.json();

                if (data.success && data.authUrl) {
                    // 跳转到OAuth授权页面
                    window.location.href = data.authUrl;
                } else {
                    throw new Error(data.error || '登录失败');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError(error.message || '网络错误，请稍后重试');
                showLoading(false);
            }
        }

        // 处理管理员登录
        async function handleAdminLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;
            
            adminErrorMessage.classList.remove('active');

            try {
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // 登录成功，跳转到管理后台
                    window.location.href = 'admin.html';
                } else {
                    throw new Error(data.error || '登录失败');
                }
            } catch (error) {
                console.error('Admin login error:', error);
                showError(error.message || '登录失败，请检查用户名和密码', true);
            }
        }

        // 显示/隐藏管理员登录模态框
        function toggleAdminModal(show) {
            if (show) {
                adminLoginModal.classList.add('active');
                document.getElementById('adminUsername').focus();
            } else {
                adminLoginModal.classList.remove('active');
                adminLoginForm.reset();
                adminErrorMessage.classList.remove('active');
            }
        }

        // 检查是否已登录
        async function checkAuth() {
            try {
                const response = await fetch(`${API_URL}/api/user`, {
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.user) {
                        // 已登录，根据是否是管理员跳转到不同页面
                        if (data.user.is_admin) {
                            window.location.href = 'admin.html';
                        } else {
                            window.location.href = 'index.html';
                        }
                    }
                }
            } catch (error) {
                console.error('Auth check error:', error);
            }
        }

        // 绑定事件
        loginBtn.addEventListener('click', handleLogin);
        
        if (adminLoginTrigger) {
            adminLoginTrigger.addEventListener('click', () => {
                console.log('管理员登录按钮被点击');
                toggleAdminModal(true);
            });
            console.log('管理员登录按钮事件已绑定');
        } else {
            console.error('找不到管理员登录按钮元素');
        }
        
        if (cancelAdminLogin) {
            cancelAdminLogin.addEventListener('click', () => toggleAdminModal(false));
        }
        
        if (adminLoginForm) {
            adminLoginForm.addEventListener('submit', handleAdminLogin);
        }

        // 点击模态框外部关闭
        adminLoginModal.addEventListener('click', (e) => {
            if (e.target === adminLoginModal) {
                toggleAdminModal(false);
            }
        });

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', () => {
            checkAuth();
        });

        // 键盘事件
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !loginBtn.disabled && !adminLoginModal.classList.contains('active')) {
                handleLogin();
            }
        });

        // ESC键关闭模态框，Ctrl+A键打开管理员登录
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && adminLoginModal.classList.contains('active')) {
                toggleAdminModal(false);
            }
            
            // Ctrl+A 打开管理员登录模态框
            if (e.ctrlKey && e.key === 'a') {
                e.preventDefault();
                console.log('Ctrl+A 快捷键触发管理员登录');
                toggleAdminModal(true);
            }
        });
    </script>
</body>

</html>